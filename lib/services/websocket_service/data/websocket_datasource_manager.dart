import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/services/websocket_service/data/websocket_datasource.dart';
import 'package:flutter_audio_room/shared/data/local/storage_service.dart';
import 'package:flutter_audio_room/shared/domain/models/either.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';
import 'package:flutter_audio_room/shared/exceptions/app_exception.dart';

/// WebSocket data source manager
/// 管理多个WebSocketDataSource实例，每个URL对应一个实例
/// 设计为单例使用，配合单连接的WebSocketRepository
/// 不再使用引用计数，简化连接管理
class WebSocketDataSourceManager {
  final Map<String, WebSocketDataSource> _dataSources = {};
  final StorageService _storageService;

  WebSocketDataSourceManager({
    required StorageService storageService,
  }) : _storageService = storageService;

  /// 获取指定URL的数据源，如果不存在则创建
  WebSocketDataSource getDataSource(String url) {
    if (!_dataSources.containsKey(url)) {
      _dataSources[url] =
          WebSocketDataSourceImpl(storageService: _storageService, url: url);
      LogUtils.d('Created new WebSocketDataSource for URL: $url',
          tag: 'WebSocketDataSourceManager.getDataSource');
    }

    return _dataSources[url]!;
  }

  /// 移除指定URL的数据源
  Future<void> removeDataSource(String url) async {
    if (!_dataSources.containsKey(url)) {
      return;
    }

    final dataSource = _dataSources[url]!;
    dataSource.dispose();
    _dataSources.remove(url);
    LogUtils.d('Removed WebSocketDataSource for URL: $url',
        tag: 'WebSocketDataSourceManager.removeDataSource');
  }

  /// 断开并移除所有数据源
  Future<void> dispose() async {
    for (final url in _dataSources.keys.toList()) {
      await removeDataSource(url);
    }
    _dataSources.clear();
    LogUtils.d('Disposed all WebSocketDataSources',
        tag: 'WebSocketDataSourceManager.dispose');
  }

  /// 检查指定URL的连接状态
  bool isConnected(String url) {
    return _dataSources[url]?.isConnected ?? false;
  }

  /// 获取所有活跃连接的URL
  List<String> get activeConnections {
    return _dataSources.entries
        .where((entry) => entry.value.isConnected)
        .map((entry) => entry.key)
        .toList();
  }

  /// 连接到指定URL
  Future<ResultWithData<WebSocketDataSource>> connect(String url) async {
    final dataSource = getDataSource(url);
    await dataSource.connect();
    return Right(dataSource);
  }

  /// 断开指定URL的连接
  Future<ResultWithData<void>> disconnect(String url) async {
    if (!_dataSources.containsKey(url)) {
      return Either.left(AppException(
        statusCode: 404,
        message: 'No connection found for URL: $url',
        identifier: 'WS_CONNECTION_NOT_FOUND',
      ));
    }

    final result =
        await _dataSources[url]!.disconnect(closeCode: CloseCode.goingAway);
    await removeDataSource(url);
    return result;
  }

  /// 获取当前活跃的数据源数量
  int get activeDataSourceCount => _dataSources.length;



  /// 检查所有WebSocket连接并尝试重新连接断开的连接
  ///
  /// 返回结果:
  /// - 成功: Either.right(null)
  /// - 失败: Either.left(AppException) - 如果没有连接或者部分重连失败
  Future<void> checkAllConnections() async {
    LogUtils.d("Checking all WebSocket connections...",
        tag: "WebSocketDataSourceManager.checkAllConnections");

    if (_dataSources.isEmpty) {
      LogUtils.d("No WebSocket connections to check",
          tag: "WebSocketDataSourceManager.checkAllConnections");
    }

    for (final url in _dataSources.keys.toList()) {
      final dataSource = _dataSources[url];
      if (dataSource == null) continue;

      final isConnected = dataSource.isConnected;
      LogUtils.d("Connection to $url is ${isConnected ? "active" : "inactive"}",
          tag: "WebSocketDataSourceManager.checkAllConnections");

      if (!isConnected) {
        final result = await connect(url);
        result.fold(
          (error) {
            LogUtils.e("Failed to reconnect to $url: ${error.message}",
                tag: "WebSocketDataSourceManager.checkAllConnections");
          },
          (_) {
            LogUtils.d("Successfully reconnected to $url",
                tag: "WebSocketDataSourceManager.checkAllConnections");
          },
        );
      }
    }
  }

  /// 重置所有WebSocket连接的重连状态
  /// 通常在应用从后台恢复到前台时调用
  void resetAllReconnectionStates() {
    LogUtils.d("Resetting reconnection states for all WebSocket connections",
        tag: "WebSocketDataSourceManager.resetAllReconnectionStates");

    for (final dataSource in _dataSources.values) {
      dataSource.resetReconnectionState();
    }
  }
}
