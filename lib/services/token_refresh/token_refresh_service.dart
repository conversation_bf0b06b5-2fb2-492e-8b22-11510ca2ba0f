import 'dart:async';

import 'package:dio/dio.dart';
import 'package:easy_debounce/easy_throttle.dart';
import 'package:flutter_audio_room/core/mixins/exception_handler_mixin.dart';
import 'package:flutter_audio_room/core/router/app_router.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/features/app_settings/screen/app_settings_screen.dart';
import 'package:flutter_audio_room/flavors.dart';
import 'package:flutter_audio_room/main.dart';
import 'package:flutter_audio_room/services/device_info/i_device_info_service.dart';
import 'package:flutter_audio_room/services/package_info/i_package_info_service.dart';
import 'package:flutter_audio_room/shared/data/local/storage_service.dart';
import 'package:flutter_audio_room/shared/data/remote/remote.dart';
import 'package:flutter_audio_room/shared/data/remote/request_queue.dart';
import 'package:flutter_audio_room/shared/domain/models/either.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';
import 'package:flutter_audio_room/shared/exceptions/app_exception.dart';
import 'package:flutter_audio_room/shared/globals.dart';

/// 请求队列管理Mixin
/// 负责处理请求队列的暂停和恢复操作
mixin RequestQueueManagerMixin {
  RequestQueue? _requestQueue;
  bool _isRefreshing = false;

  /// 设置请求队列
  void setRequestQueue(RequestQueue queue) {
    _requestQueue = queue;
  }

  /// 暂停请求队列
  void pauseRequestQueue() {
    if (_requestQueue == null) return;
    _isRefreshing = true;
    _requestQueue!.pause();
  }

  /// 恢复请求队列
  void resumeRequestQueue() {
    if (_requestQueue == null || !_isRefreshing) return;
    _isRefreshing = false;
    _requestQueue!.resume();
  }

  /// 获取是否正在刷新状态
  bool get isRefreshing => _isRefreshing;
}

/// Token刷新服务
/// 负责管理访问令牌的刷新和相关网络请求
class TokenRefreshService extends NetworkService
    with ExceptionHandlerMixin, RequestQueueManagerMixin {
  final Dio _dio;
  final StorageService storageService;
  final IDeviceInfoService deviceInfoService;
  final IPackageInfoService packageInfoService;

  // 并发控制
  Completer<ResultWithData<String>>? _refreshCompleter;
  DateTime? _lastTokenRefreshTime;
  static const Duration _tokenRefreshThrottleDuration = Duration(minutes: 1);

  final List<String> _refreshTokenExpiredCodes = [
    'refresh.token.replaced',
    'token.expired'
  ];

  TokenRefreshService(this._dio, this.storageService, this.deviceInfoService,
      this.packageInfoService) {
    _initializeDio();
  }

  /// 初始化Dio客户端
  void _initializeDio() {
    _dio.options = BaseOptions(
      baseUrl: baseUrl,
      headers: headers,
      connectTimeout: const Duration(seconds: 10),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 10),
    );
  }

  /// 重定向到登录页面
  void toLogin({String? message}) {
    EasyThrottle.throttle(
      'TokenRefreshService.toLogin',
      const Duration(seconds: 1),
      () async {
        final context = AppRouter.navigatorKey.currentContext;
        if (context == null) return;

        final logoutController = LogoutController(providerContainer, context);
        logoutController.logout(message: message);
      },
    );
  }

  /// 刷新令牌，添加了并发控制
  Future<ResultWithData<String>> refreshToken({int maxRetryCount = 3}) async {
    // 检查是否需要刷新
    final now = DateTime.now();
    final shouldRefreshToken = _lastTokenRefreshTime == null ||
        now.difference(_lastTokenRefreshTime!) > _tokenRefreshThrottleDuration;
        
    // 如果已经有刷新请求在进行中，返回现有的Future
    if (_refreshCompleter != null && !shouldRefreshToken) {
      return _refreshCompleter!.future;
    }
    
    _lastTokenRefreshTime = now;
    _refreshCompleter = Completer<ResultWithData<String>>();

    // 暂停请求队列
    pauseRequestQueue();

    try {
      final result = await _executeTokenRefreshWithRetry(maxRetryCount);
      _completeRefresh(result);
      return result;
    } catch (e) {
      final result = Left<AppException, String>(AppException(
        message: '刷新令牌时发生未知错误: ${e.toString()}',
        statusCode: 500,
        identifier: 'TokenRefreshService',
      ));
      _completeRefresh(result);
      return result;
    } finally {
      // 恢复请求队列
      resumeRequestQueue();
    }
  }

  /// 使用重试机制执行token刷新
  Future<ResultWithData<String>> _executeTokenRefreshWithRetry(
      int maxRetryCount) async {
    int remainingRetries = maxRetryCount;

    while (true) {
      final result = await _performTokenRefresh();

      final shouldRetry = result.fold(
        (exception) =>
            _shouldRetryForException(exception) && remainingRetries > 0,
        (token) => token.isEmpty && remainingRetries > 0,
      );

      if (shouldRetry) {
        remainingRetries--;
        await Future.delayed(const Duration(seconds: 1));
        continue;
      }

      return result;
    }
  }

  /// 执行实际的token刷新请求
  Future<ResultWithData<String>> _performTokenRefresh() async {
    final refreshTokenString = storageService.refreshToken;
    if (refreshTokenString.isEmpty) {
      toLogin();
      return const Left<AppException, String>(AppException(
        message: '刷新令牌未找到',
        statusCode: 404,
        identifier: 'TokenRefreshService',
      ));
    }

    final res = await post(
      '/profile/auth/refreshToken',
      data: {'refreshToken': refreshTokenString},
    );

    return res.fold(
      (exception) {
        if (_refreshTokenExpiredCodes.contains(exception.identifier)) {
          toLogin();
        }
        return Left<AppException, String>(exception);
      },
      (response) async {
        final accessToken = response['accessToken'] ?? '';
        if (accessToken.isEmpty) {
          return const Left<AppException, String>(AppException(
            message: '访问令牌为空',
            statusCode: 404,
            identifier: 'TokenRefreshService',
          ));
        }

        await storageService.setAccessToken(accessToken);
        return Right(accessToken);
      },
    );
  }

  /// 判断是否应该为特定异常重试
  bool _shouldRetryForException(AppException exception) {
    return !_refreshTokenExpiredCodes.contains(exception.identifier);
  }
  
  /// 完成刷新过程并完成Completer
  void _completeRefresh(ResultWithData<String> result) {
    if (_refreshCompleter != null && !_refreshCompleter!.isCompleted) {
      _refreshCompleter!.complete(result);
    }
  }

  /// 当需要刷新token时调用
  Future<void> onTokenRefreshNeeded() async {
    if (isRefreshing) return;
    pauseRequestQueue();
  }

  /// 当token刷新完成时调用
  void onTokenRefreshCompleted() {
    if (!isRefreshing) return;
    LogUtils.d(
        '令牌刷新完成。队列状态: 大小=${_requestQueue?.queueSize}, '
        '活跃请求=${_requestQueue?.activeRequests}, '
        '已暂停=${_requestQueue?.isPaused}, ',
        tag: 'TokenRefreshService');
    resumeRequestQueue();
  }

  @override
  String get baseUrl => F.connectionString;

  @override
  Map<String, Object> get headers => {
        'accept': 'application/json',
        'content-type': 'application/json',
        'GK-platform': deviceInfoService.platform,
        'GK-app-version': packageInfoService.version,
      };

  @override
  Future<ResultWithData<Map<String, dynamic>>> get(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
  }) {
    final res = handleException(
      () => _dio.get(
        endpoint,
        queryParameters: queryParameters,
      ),
      endpoint: endpoint,
    );
    return res;
  }

  @override
  Future<ResultWithData<Map<String, dynamic>>> post(String endpoint,
      {Map<String, dynamic>? data}) {
    final res = handleException(
      () => _dio.post(
        endpoint,
        data: data,
      ),
      endpoint: endpoint,
    );
    return res;
  }

  @override
  Map<String, dynamic>? updateHeader(Map<String, dynamic> data) {
    final header = {...headers, ...data};
    if (!kTestMode) {
      _dio.options.headers = header;
    }
    return header;
  }

  @override
  Future<ResultWithData<Map<String, dynamic>>> uploadFile(
    String endpoint, {
    required String filePath,
    Map<String, dynamic>? queryParameters,
    String? filename,
    String formName = 'file',
    Map<String, dynamic>? extraData,
    void Function(int count, int total)? onSendProgress,
  }) {
    throw UnimplementedError();
  }
}
