// Repository interface for Socket.IO operations
abstract class SocketIORepository {
  // Initialize socket connection
  void initializeSocket(String url);

  // Send message through socket
  void emit(String event, Map<String, dynamic> data);

  void emitWithAck(
      String event, Map<String, dynamic> data, Function(dynamic) ack);

  // Listen to specific event
  void listenToEvent(String event, Function(dynamic) handler);

  // Stop listening to specific event
  void stopListening(String event);

  void onConnect(dynamic Function(dynamic) handler);

  void onReconnect(dynamic Function(dynamic) handler);

  void onConnectError(dynamic Function(dynamic) handler);

  void onDisconnect(dynamic Function(dynamic) handler);

  void onError(dynamic Function(dynamic) handler);

  // Disconnect socket
  void disconnect();

  void dispose();

  // Check connection status
  bool get isConnected;

  // Reconnect socket with new token
  void reconnectWithNewToken(String newToken);

  void connect();
}
