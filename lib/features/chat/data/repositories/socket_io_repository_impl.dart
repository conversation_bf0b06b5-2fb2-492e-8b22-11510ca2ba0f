import '../../domain/repositories/socket_io_repository.dart';
import '../datasources/socket_io_datasource.dart';

class SocketIORepositoryImpl implements SocketIORepository {
  final SocketIODataSource _dataSource;

  SocketIORepositoryImpl(this._dataSource);

  @override
  void initializeSocket(String url) {
    _dataSource.initializeSocket(url);
  }

  @override
  void emit(String event, Map<String, dynamic> data) {
    _dataSource.emit(event, data);
  }

  @override
  void emitWithAck(
      String event, Map<String, dynamic> data, Function(dynamic) ack) {
    _dataSource.emitWithAck(event, data, ack);
  }

  @override
  void listenToEvent(String event, Function(dynamic) handler) {
    _dataSource.on(event, handler);
  }

  @override
  void stopListening(String event) {
    _dataSource.off(event);
  }

  @override
  void onConnect(dynamic Function(dynamic) handler) {
    _dataSource.onConnect(handler);
  }

  @override
  void onReconnect(dynamic Function(dynamic) handler) {
    _dataSource.onReconnect(handler);
  }

  @override
  void onConnectError(dynamic Function(dynamic) handler) {
    _dataSource.onConnectError(handler);
  }

  @override
  void onDisconnect(dynamic Function(dynamic) handler) {
    _dataSource.onDisconnect(handler);
  }

  @override
  void onError(dynamic Function(dynamic) handler) {
    _dataSource.onError(handler);
  }

  @override
  void connect() {
    _dataSource.connect();
  }

  @override
  void disconnect() {
    _dataSource.disconnect();
  }

  @override
  void dispose() {
    _dataSource.dispose();
  }

  @override
  bool get isConnected => _dataSource.isConnected;

  @override
  void reconnectWithNewToken(String newToken) {
    _dataSource.reconnectWithNewToken(newToken);
  }
}
