import 'dart:async';
import 'dart:convert';

import 'package:flutter_audio_room/core/di/app_module.dart';
import 'package:flutter_audio_room/core/utils/loading_utils.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/features/authentication/presentation/providers/auth_providers.dart';
import 'package:flutter_audio_room/features/instant_chat/domain/entities/match_status.dart';
import 'package:flutter_audio_room/features/instant_chat/presentation/provider/instant_chat_state.dart';
import 'package:flutter_audio_room/services/core_service/core_service_provider.dart';
import 'package:flutter_audio_room/services/punishment_service/model/punishment_exception.dart';
import 'package:flutter_audio_room/services/punishment_service/model/punishment_model.dart';
import 'package:flutter_audio_room/services/punishment_service/punishment_service.dart';
import 'package:flutter_audio_room/services/websocket_service/core/constants/websocket_url.dart';
import 'package:flutter_audio_room/services/websocket_service/data/websocket_datasource.dart';
import 'package:flutter_audio_room/services/websocket_service/data/websocket_datasource_manager.dart';
import 'package:flutter_audio_room/services/websocket_service/domain/entities/websocket_entity.dart';
import 'package:flutter_audio_room/shared/domain/models/response.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'instant_chat_provider.g.dart';

@riverpod
class InstantChat extends _$InstantChat {
  Timer? _matchTimer;
  WebSocketDataSource? _websocketDatasource;
  late final WebSocketUrl _webSocketUrl;
  StreamSubscription<WebsocketResponse>? messageStreamSubscription;

  @override
  InstantChatState build() {
    final myUserId = ref.read(accountProvider).userInfo?.profile?.id;
    _webSocketUrl = InstantChatWebSocketUrl();

    ref.onDispose(() {
      dispose();
    });

    if (myUserId == null) {
      return InstantChatState(
        matchStatus: MatchError('User info not found'),
        myUserId: '',
      );
    }

    return InstantChatState(
      myUserId: myUserId,
    );
  }

  Future<void> initializeWebSocket() async {
    final result =
        await getIt<WebSocketDataSourceManager>().connect(_webSocketUrl.url);
    _websocketDatasource = result.getRight();
    messageStreamSubscription =
        _websocketDatasource?.messageStream
        .listen(_handleWebSocketMessage);
  }

  void _handleWebSocketMessage(WebsocketResponse response) {
    _disposeTimer();

    final data = response.data.extraData;
    final code = response.code ?? '';
    final message = response.msg ?? '';

    if (getIt<PunishmentService>().isPunishmentCode(code)) {
      final punishment = response.data.punishment ?? const PunishmentModel();
      state = state.copyWith(
        matchStatus: MatchRestricted(
          message: message,
          code: code,
          expireTime: punishment.expireTime,
        ),
        punishment: PunishmentException(
          punishment: punishment,
          identifier: code,
          message: message,
        ),
      );
      return;
    }

    if (response.success == false) {
      LogUtils.e('WebSocket error: $message', tag: 'WebSocket');
      LoadingUtils.showToast(message);
      return;
    }

    final shouldClose = response.shouldClose;

    if (shouldClose == true) {
      state = state.copyWith(
        matchStatus: MatchError(message),
      );
      return;
    }

    final handlers = {
      '${_webSocketUrl.paramsPath}/startMatch': _handleMatch,
      '${_webSocketUrl.paramsPath}/cancel': _handleCancel,
    };

    final handler = handlers[response.path];
    if (handler != null) {
      handler(data);
    }
  }

  Future<void> startMatch() async {
    await _sendWebSocketMessage(
      path: '/startMatch',
      data: {},
      onSuccess: () => state = state.copyWith(matchStatus: MatchInProgress()),
    );

    final timeoutSeconds = ref
        .read(coreServiceProvider)
        .config
        .rtcConfigResp
        .instantChatConfigResp
        .pendingTimeoutSeconds;

    _disposeTimer();
    _matchTimer = Timer(Duration(seconds: timeoutSeconds), () async {
      if (state.matchStatus is MatchSuccess) {
        _disposeTimer();
        return;
      }
      state = state.copyWith(
        matchStatus: MatchError('Match timeout, please try again'),
      );
    });

    
  }

  Future<void> cancelMatch() async {
    if (state.matchStatus is MatchSuccess) {
      final matchId = (state.matchStatus as MatchSuccess).matchId;
      await _sendWebSocketMessage(
        path: '/cancel',
        data: {
          'matchId': matchId,
        },
      );

      state = state.copyWith(matchStatus: const MatchNotStarted());
      await _disposeWebSocket();
      _disposeTimer();
    }
  }

  Future<void> _handleMatch(Map<String, dynamic> message) async {
    _matchTimer?.cancel();

    final matchId = message['matchId'] as String?;
    final peerId = message['userId'] as String?;
    final peerNickName = message['nickName'] as String?;
    // final interests = message['interests'] as List<dynamic>?;
    final Map<String, dynamic> peerAvatar = message['avatar'] ?? {};

    if (matchId == null) return;
    
    state = state.copyWith(
      matchStatus: MatchSuccess(matchId),
      peerId: peerId,
      peerNickName: peerNickName,
      peerAvatar: peerAvatar,
    );
  }

  Future<void> _handleCancel(Map<String, dynamic> data) async {
    LogUtils.d('peer cancel match', tag: 'WebSocket');
  }

  Future<void> _sendWebSocketMessage({
    required String path,
    required Map<String, dynamic> data,
    Function()? onSuccess,
  }) async {
    final message = {'path': '${_webSocketUrl.paramsPath}$path', ...data};
    final result = await _websocketDatasource?.sendMessage(
      WebSocketMessageEntity(
        data: jsonEncode(message),
        type: WebSocketMessageType.json,
      ),
    );

    result?.fold(
      (error) => {
        state = state.copyWith(
          matchStatus: MatchError(error.message),
        ),
      },
      (response) {
        onSuccess?.call();
      },
    );
  }

  Future<void> _disposeWebSocket() async {
    await messageStreamSubscription?.cancel();
    await _websocketDatasource?.dispose();
  }

  void _disposeTimer() {
    _matchTimer?.cancel();
    _matchTimer = null;
  }

  void dispose() {
    _disposeTimer();
    _disposeWebSocket();
  }
}
