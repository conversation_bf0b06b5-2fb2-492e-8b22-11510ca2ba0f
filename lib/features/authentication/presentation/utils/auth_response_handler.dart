import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/extensions/navigator_ext.dart';
import 'package:flutter_audio_room/core/router/app_router.dart';
import 'package:flutter_audio_room/core/utils/loading_utils.dart';
import 'package:flutter_audio_room/features/authentication/data/model/profile_model.dart';
import 'package:flutter_audio_room/features/authentication/data/model/user_info_model.dart';
import 'package:flutter_audio_room/features/authentication/presentation/providers/auth_providers.dart';
import 'package:flutter_audio_room/features/home/<USER>/screens/home_screen.dart';
import 'package:flutter_audio_room/main.dart';
import 'package:flutter_audio_room/services/punishment_service/model/punishment_exception.dart';
import 'package:flutter_audio_room/services/punishment_service/model/restrict_exception.dart';
import 'package:flutter_audio_room/shared/domain/models/either.dart';
import 'package:flutter_audio_room/shared/exceptions/app_exception.dart';

/// Utility class for handling authentication API responses directly
/// without relying on global state listeners
class AuthResponseHandler {
  static BuildContext get currentContext =>
      AppRouter.navigatorKey.currentContext!;

  /// Handle authentication result and perform appropriate UI actions
  static Future<void> handleAuthResult({
    required Either<AppException, UserInfoModel> result,
    VoidCallback? onAccountNotExist,
  }) async {
    LoadingUtils.dismiss();

    result.fold(
      (error) => _handleAuthError(
        error: error,
        onAccountNotExist: onAccountNotExist,
      ),
      (user) => _handleAuthSuccess(
        user: user,
      ),
    );
  }

  /// Handle authentication error
  static void _handleAuthError({
    required AppException error,
    VoidCallback? onAccountNotExist,
  }) {
    if (error.identifier.contains('account.not.exist')) {
      onAccountNotExist?.call();
    } else if (error is PunishmentException) {
      currentContext.showPunishmentDialog(error);
    } else if (error is RestrictException) {
      currentContext.showRestrictDialog(error);
    } else {
      LoadingUtils.showToast(error.message.toString());
    }
  }

  /// Handle authentication success
  static Future<void> _handleAuthSuccess({
    required UserInfoModel user,
  }) async {
    FocusScope.of(currentContext).unfocus();

    final profile = user.profile;
    if (profile?.deleteStatus == DeleteStatus.pending) {
      await _handleAccountDeletionPending();
    } else if (profile?.deleteStatus == DeleteStatus.hidden) {
      await _handleAccountHidden();
    } else {
      currentContext.pushAndRemoveUntil(
        const WidgetPageConfig(page: HomeScreen()),
        predicate: (route) => false,
      );
    }
  }

  /// Handle account deletion pending state
  static Future<void> _handleAccountDeletionPending() async {
    final result = await currentContext.showOkCancelAlertDialog(
      title: 'Account Deletion in Progress',
      content:
          'You can choose to restore your account, which will interrupt the account deletion process. Would you like to restore?',
      confirmText: 'Restore',
      cancelText: 'Exit',
    );

    if (currentContext.mounted) {
      if (result == true) {
        LoadingUtils.showLoading();
        final restoreResult = await providerContainer
            .read(accountProvider.notifier)
            .restoreAccount();

        restoreResult.fold(
          (error) async {
            await providerContainer.read(accountProvider.notifier).logout();
            LoadingUtils.showToast(error.message.toString());
          },
          (user) {
            LoadingUtils.dismiss();
            currentContext.pushAndRemoveUntil(
              const WidgetPageConfig(page: HomeScreen()),
              predicate: (route) => false,
            );
          },
        );
      } else {
        await providerContainer.read(accountProvider.notifier).logout();
      }
    }
  }

  /// Handle account hidden state
  static Future<void> _handleAccountHidden() async {
    final result = await currentContext.showOkCancelAlertDialog(
      title: 'Account Hidden',
      content:
          'Your account has been hidden. Would you like to restore? You can restore your account by entering the verification code sent to your email.',
      confirmText: 'Restore',
      cancelText: 'Exit',
    );

    if (currentContext.mounted) {
      if (result == true) {
        LoadingUtils.showLoading();
        final unHideResult = await providerContainer
            .read(accountProvider.notifier)
            .unHideAccount();

        unHideResult.fold(
          (error) async {
            await providerContainer.read(accountProvider.notifier).logout();
            LoadingUtils.showToast(error.message.toString());
          },
          (user) {
            currentContext.pushAndRemoveUntil(
              const WidgetPageConfig(page: HomeScreen()),
              predicate: (route) => false,
            );
            LoadingUtils.dismiss();
          },
        );
      } else {
        await providerContainer.read(accountProvider.notifier).logout();
      }
    }
  }
}
