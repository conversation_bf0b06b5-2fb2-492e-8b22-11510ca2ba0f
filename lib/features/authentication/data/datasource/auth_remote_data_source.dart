import 'package:flutter_audio_room/features/authentication/data/model/contact_info_model.dart';
import 'package:flutter_audio_room/features/authentication/data/model/signup_profile_model.dart';
import 'package:flutter_audio_room/features/authentication/data/model/user_info_model.dart';
import 'package:flutter_audio_room/features/authentication/data/model/user_level_model.dart';
import 'package:flutter_audio_room/features/authentication/data/model/wallet_model.dart';
import 'package:flutter_audio_room/services/gift_service/data/model/bag_gift_model.dart';
import 'package:flutter_audio_room/shared/data/remote/remote.dart';
import 'package:flutter_audio_room/shared/domain/models/either.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';
import 'package:flutter_audio_room/shared/exceptions/app_exception.dart';

enum AuthType {
  phone,
  email,
}

abstract class LoginUserDataSource {
  Future<VoidResult> checkUsername({required String username});

  /// send code
  Future<ResultWithData<String?>> sendCode({
    required String sendTo,
    String? countryCode,
    required AuthType type,
  });

  Future<ResultWithData<UserInfoModel>> signup({
    required SignupProfileModel profile,
    required AuthType type,
  });

  Future<ResultWithData<UserInfoModel>> login({
    required SignupProfileModel profile,
    required AuthType type,
  });

  Future<ResultWithData<Map<String, dynamic>?>> refreshToken({
    required String refreshToken,
  });

  Future<VoidResult> logout();

  Future<ResultWithData<ContactInfoModel>> getContactInfo();

  Future<VoidResult> sendVerificationCode2Self({
    required bool isPhone,
  });

  Future<VoidResult> deleteAccount({
    required bool useEmail,
    required String code,
  });

  Future<VoidResult> hideAccount({
    required bool useEmail,
    required String code,
  });

  Future<VoidResult> unHideAccount();

  Future<VoidResult> restoreAccount();

  Future<VoidResult> resetPassword({
    required bool isPhone,
    required String code,
    required String password,
  });

  Future<VoidResult> updateIpAddress({required String ipAddress});

  Future<VoidResult> updateLocation({
    required String countryCode,
    required String cityName,
    required double latitude,
    required double longitude,
  });

  Future<ResultWithData<UserLevelModel>> getUserLevel();

  Future<ResultWithData<BagGiftModel>> getCurrentFrame();

  Future<ResultWithData<WalletModel>> getUserWallet();
}

class LoginUserRemoteDataSource implements LoginUserDataSource {
  final NetworkService networkService;

  LoginUserRemoteDataSource(this.networkService);

  final String _urlPrefix = '/profile';

  @override
  Future<VoidResult> checkUsername(
      {required String username}) async {
    try {
      final eitherType = await networkService.post(
        '$_urlPrefix/auth/checkNickname',
        data: <String, dynamic>{
          'nickName': username,
        },
      );

      return eitherType.fold(
        (exception) => Left(exception),
        (response) => const Right(null),
      );
    } catch (e) {
      return Left(AppException(
        message: 'Unknown error occurred',
        statusCode: 1,
        identifier: '${e.toString()}\nLoginUserRemoteDataSource.checkUsername',
      ));
    }
  }

  @override
  Future<ResultWithData<UserInfoModel>> signup({
    required SignupProfileModel profile,
    required AuthType type,
  }) async {
    
    final payload = profile.toSignUpPayload();

    try {
      final eitherType = await networkService.post(
        '$_urlPrefix/auth/register/${type.name}',
        data: payload,
      );

      return eitherType.fold(
        (exception) {
          return Left(exception);
        },
        (response) {
          try {
            final userInfo = UserInfoModel.fromJson(response);
            return Right(userInfo);
          } catch (e) {
            return const Left(AppException(
              message: 'Invalid response',
              statusCode: 400,
              identifier: 'LoginUserRemoteDataSource.signup',
            ));
          }
        },
      );
    } catch (e) {
      return Left(
        AppException(
          message: 'Unknown error occurred',
          statusCode: 1,
          identifier: '${e.toString()}\nLoginUserRemoteDataSource.signupPhone',
        ),
      );
    }
  }

  @override
  Future<ResultWithData<UserInfoModel>> login({
    required SignupProfileModel profile,
    required AuthType type,
  }) async {
    try {
      final payload = profile.toLoginPayload();
      final isPasswordLogin = profile.password != null;
      final path =
          '$_urlPrefix/auth/login/${type.name}/${isPasswordLogin ? 'password' : 'code'}';

      final eitherType = await networkService.post(
        path,
        data: payload,
      );
      
      return eitherType.fold(
        (exception) => Left(exception),
        (response) {
          try {
            final userInfo = UserInfoModel.fromJson(response);
            return Right(userInfo);
          } catch (e) {
            return const Left(AppException(
              message: 'Invalid response',
              statusCode: 400,
              identifier: 'LoginUserRemoteDataSource.login',
            ));
          }
        },
      );
    } catch (e) {
      return Left(
        AppException(
          message: 'Unknown error occurred',
          statusCode: 1,
          identifier: '${e.toString()}\nLoginUserRemoteDataSource.login',
        ),
      );
    }
  }

  @override
  Future<ResultWithData<Map<String, dynamic>?>> refreshToken({
    required String refreshToken,
  }) async {
    try {
      final eitherType = await networkService.post(
        '$_urlPrefix/auth/refreshToken',
        data: <String, dynamic>{
          'refreshToken': refreshToken,
        },
      );

      return eitherType.fold(
        (exception) => Left(exception),
        (response) => Right(response),
      );
    } catch (e) {
      return Left(
        AppException(
          message: 'Unknown error occurred',
          statusCode: 1,
          identifier: '${e.toString()}\nLoginUserRemoteDataSource.refreshToken',
        ),
      );
    }
  }

  @override
  Future<ResultWithData<String?>> sendCode({
    required String sendTo,
    String? countryCode,
    required AuthType type,
  }) async {
    final payload = <String, dynamic>{};

    if (type == AuthType.phone && countryCode != null) {
      payload['phone'] = sendTo;
      payload['countryNumber'] = countryCode;
    } else if (type == AuthType.email) {
      payload['email'] = sendTo;
    } else {
      return const Left(AppException(
        message: 'Invalid parameters for sendCode',
        statusCode: 400,
        identifier: 'LoginUserRemoteDataSource.sendCode',
      ));
    }

    try {
      final eitherType = await networkService.post(
        '$_urlPrefix/auth/sendVerificationCode/${type.name}',
        data: payload,
      );
      return eitherType.fold(
        (exception) {
          return Left(exception);
        },
        (response) {
          return Right(response['msg']);
        },
      );
    } catch (e) {
      return Left(
        AppException(
          message: 'Unknown error occurred',
          statusCode: 1,
          identifier: '${e.toString()}\nLoginUserRemoteDataSource.sendCode',
        ),
      );
    }
  }

  @override
  Future<VoidResult> logout() async {
    try {
      final eitherType = await networkService.post('$_urlPrefix/auth/logout');
      return eitherType.fold(
        (exception) {
          return Left(exception);
        },
        (response) {
          return const Right(null);
        },
      );
    } catch (e) {
      return Left(
        AppException(
          message: 'Unknown error occurred',
          statusCode: 1,
          identifier: '${e.toString()}\nLoginUserRemoteDataSource.logout',
        ),
      );
    }
  }

  @override
  Future<VoidResult> updateIpAddress({required String ipAddress}) async {
    return networkService.post(
      '$_urlPrefix/profile/ipLocationUpdate',
      data: <String, dynamic>{
        'ip': ipAddress,
      },
    );
  }

  @override
  Future<VoidResult> updateLocation({
    required String countryCode,
    required String cityName,
    required double latitude,
    required double longitude,
  }) async {
    return networkService.post(
      '$_urlPrefix/profile/gpsLocationUpdate',
      data: <String, dynamic>{
        'countryCode': countryCode,
        'cityName': cityName,
        'latitude': latitude,
        'longitude': longitude,
      },
    );
  }

  @override
  Future<ResultWithData<UserLevelModel>> getUserLevel() async {
    final res =
        await networkService.post('$_urlPrefix/canary/user/levelDetail');
    return res.fold(
      (l) => Left(l),
      (r) => Right(UserLevelModel.fromJson(r)),
    );
  }

  @override
  Future<ResultWithData<BagGiftModel>> getCurrentFrame() async {
    final res = await networkService.get('/canary/user/getCurAvatarFrame');
    return res.fold(
      (l) => Left(l),
      (r) => Right(BagGiftModel.fromJson(r)),
    );
  }

  @override
  Future<ResultWithData<WalletModel>> getUserWallet() async {
    final res = await networkService.get('$_urlPrefix/wallet/info');
    return res.fold(
      (l) => Left(l),
      (r) {
        try {
          final wallet = WalletModel.fromJson(r);
          return Right(wallet);
        } catch (e) {
          return Left(AppException(
            message: e.toString(),
            statusCode: 400,
            identifier: 'LoginUserRemoteDataSource.getUserWallet',
          ));
        }
      },
    );
  }

  @override
  Future<ResultWithData<ContactInfoModel>> getContactInfo() async {
    final res = await networkService.get('$_urlPrefix/profile/contactInfo');
    return res.fold(
      (l) => Left(l),
      (r) {
        try {
          final contactInfo = ContactInfoModel.fromJson(r);
          return Right(contactInfo);
        } catch (e) {
          return Left(AppException(
            message: e.toString(),
            statusCode: 400,
            identifier: 'LoginUserRemoteDataSource.getContactInfo',
          ));
        }
      },
    );
  }

  @override
  Future<VoidResult> sendVerificationCode2Self({
    required bool isPhone,
  }) async {
    return networkService.post(
      '$_urlPrefix/profile/sendVerificationCode2Self',
      data: <String, dynamic>{
        'isPhone': isPhone,
      },
    );
  }

  @override
  Future<VoidResult> deleteAccount({
    required bool useEmail,
    required String code,
  }) async {
    try {
      final eitherType = await networkService.post(
        '$_urlPrefix/profile/deleteProfile',
        data: <String, dynamic>{
          "useEmail": useEmail,
          "code": code,
        },
      );
      return eitherType.fold(
        (exception) {
          return Left(exception);
        },
        (response) {
          return const Right(null);
        },
      );
    } catch (e) {
      return Left(
        AppException(
          message: 'Unknown error occurred',
          statusCode: 1,
          identifier:
              '${e.toString()}\nLoginUserRemoteDataSource.deleteAccount',
        ),
      );
    }
  }

  @override
  Future<VoidResult> hideAccount({
    required bool useEmail,
    required String code,
  }) async {
    try {
      final eitherType = await networkService.post(
        '$_urlPrefix/profile/stashProfile',
        data: <String, dynamic>{
          "useEmail": useEmail,
          "code": code,
        },
      );
      return eitherType.fold(
        (exception) {
          return Left(exception);
        },
        (response) {
          return const Right(null);
        },
      );
    } catch (e) {
      return Left(AppException(
        message: 'Unknown error occurred',
        statusCode: 1,
        identifier: '${e.toString()}\nLoginUserRemoteDataSource.hideAccount',
      ));
    }
  }

  @override
  Future<VoidResult> unHideAccount() async {
    try {
      final eitherType = await networkService.post(
        '$_urlPrefix/profile/unstashProfile',
      );
      return eitherType.fold(
        (exception) {
          return Left(exception);
        },
        (response) {
          return const Right(null);
        },
      );
    } catch (e) {
      return Left(AppException(
        message: 'Unknown error occurred',
        statusCode: 1,
        identifier: '${e.toString()}\nLoginUserRemoteDataSource.unHideAccount',
      ));
    }
  }

  @override
  Future<VoidResult> restoreAccount() async {
    return networkService.get(
      '$_urlPrefix/profile/cancelDeleteProfile',
    );
  }

  @override
  Future<VoidResult> resetPassword({
    required bool isPhone,
    required String code,
    required String password,
  }) async {
    try {
      final eitherType = await networkService.post(
        '$_urlPrefix/profile/resetPassword',
        data: <String, dynamic>{
          "isPhone": isPhone,
          "code": code,
          "password": password,
        },
      );
      return eitherType.fold(
        (exception) {
          return Left(exception);
        },
        (response) {
          return const Right(null);
        },
      );
    } catch (e) {
      return Left(
        AppException(
          message: 'Unknown error occurred',
          statusCode: 1,
          identifier:
              '${e.toString()}\nLoginUserRemoteDataSource.resetPassword',
        ),
      );
    }
  }
}
